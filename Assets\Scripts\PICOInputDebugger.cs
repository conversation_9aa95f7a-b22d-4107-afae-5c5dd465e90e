using UnityEngine;
using UnityEngine.UI;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.XR;
#endif

/// <summary>
/// PICO输入调试器
/// 在PICO设备屏幕上显示输入状态和调试信息
/// </summary>
public class PICOInputDebugger : MonoBehaviour
{
    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;
    
    [Header("调试UI设置")]
    [SerializeField] private bool createDebugUI = true;
    [SerializeField] private bool showControllerStatus = true;
    [SerializeField] private bool showInputStatus = true;
    
    [Header("输入设置")]
    [SerializeField] private float inputCooldown = 0.5f;
    
    // UI组件
    private Canvas debugCanvas;
    private Text statusText;
    private Text inputText;
    private Text functionText;
    
    // 控制器引用
#if UNITY_XR_INTERACTION_TOOLKIT
    private XRController leftController;
    private XRController rightController;
#endif

    // 输入状态
    private bool lastTriggerState = false;
    private bool lastPrimaryState = false;
    private bool lastSecondaryState = false;
    private bool lastMenuState = false;
    private float lastInputTime = 0f;
    
    // 调试信息
    private string statusInfo = "";
    private string inputInfo = "";
    private string functionInfo = "";

    void Start()
    {
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

        if (createDebugUI)
        {
            CreateDebugUI();
        }

        InitializeControllers();

        UpdateStatusInfo("PICO输入调试器已启动");
        UpdateFunctionInfo("正在初始化...");
    }

    void Update()
    {
        CheckControllerInput();
        UpdateDebugUI();
    }

    /// <summary>
    /// 初始化控制器
    /// </summary>
    private void InitializeControllers()
    {
        UpdateFunctionInfo("开始查找控制器...");

#if UNITY_XR_INTERACTION_TOOLKIT
        var controllers = FindObjectsOfType<XRController>();
        UpdateFunctionInfo($"找到 {controllers.Length} 个XRController组件");

        if (controllers.Length == 0)
        {
            // 尝试查找所有GameObject
            var allObjects = FindObjectsOfType<GameObject>();
            int controllerObjects = 0;
            foreach (var obj in allObjects)
            {
                if (obj.name.ToLower().Contains("controller"))
                {
                    controllerObjects++;
                    UpdateFunctionInfo($"发现控制器对象: {obj.name}");
                }
            }
            UpdateFunctionInfo($"总共发现 {controllerObjects} 个控制器相关对象");
        }

        foreach (var controller in controllers)
        {
            UpdateFunctionInfo($"检查控制器: {controller.name}, 节点: {controller.controllerNode}");

            if (controller.controllerNode == XRNode.LeftHand)
            {
                leftController = controller;
                UpdateStatusInfo($"✓ 左手控制器: {controller.name}");
            }
            else if (controller.controllerNode == XRNode.RightHand)
            {
                rightController = controller;
                UpdateStatusInfo($"✓ 右手控制器: {controller.name}");
            }
            else
            {
                UpdateFunctionInfo($"未知节点控制器: {controller.name} - {controller.controllerNode}");
            }
        }

        if (leftController == null && rightController == null)
        {
            UpdateStatusInfo("⚠ 未找到任何VR控制器");
            UpdateFunctionInfo("请检查XRController组件配置");
        }
        else
        {
            string foundControllers = "";
            if (leftController != null) foundControllers += "左手 ";
            if (rightController != null) foundControllers += "右手 ";
            UpdateStatusInfo($"找到控制器: {foundControllers}");
        }
#else
        UpdateStatusInfo("⚠ XR Interaction Toolkit未安装");
        UpdateFunctionInfo("需要安装XR Interaction Toolkit");
#endif
    }

    /// <summary>
    /// 检查控制器输入
    /// </summary>
    private void CheckControllerInput()
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        // 优先检查右手控制器
        if (rightController != null)
        {
            CheckSingleControllerInput(rightController, "右手");
        }
        // 如果右手控制器不可用，检查左手控制器
        else if (leftController != null)
        {
            CheckSingleControllerInput(leftController, "左手");
        }
        else
        {
            // 如果没有找到控制器，显示详细信息
            inputInfo = "未找到可用的控制器\n请检查XRController配置";
        }
#endif
    }

    /// <summary>
    /// 检查单个控制器输入
    /// </summary>
    private void CheckSingleControllerInput(object controllerObj, string handName)
    {
#if UNITY_XR_INTERACTION_TOOLKIT
        var controller = controllerObj as XRController;
        if (controller == null) return;

        // 更新控制器状态信息
        if (showControllerStatus)
        {
            string deviceStatus = controller.inputDevice.isValid ? "有效" : "无效";
            string deviceName = controller.inputDevice.isValid ? controller.inputDevice.name : "未知";
            statusInfo = $"{handName}控制器: {deviceStatus}\n设备: {deviceName}";
        }

        if (!controller.inputDevice.isValid) return;

        var inputDevice = controller.inputDevice;
        
        // 获取按钮状态
        bool triggerPressed = false;
        bool primaryPressed = false;
        bool secondaryPressed = false;
        bool menuPressed = false;
        
        inputDevice.TryGetFeatureValue(CommonUsages.triggerButton, out triggerPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.primaryButton, out primaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.secondaryButton, out secondaryPressed);
        inputDevice.TryGetFeatureValue(CommonUsages.menuButton, out menuPressed);
        
        // 更新输入状态显示
        if (showInputStatus)
        {
            inputInfo = $"扳机: {(triggerPressed ? "按下" : "释放")}\n" +
                       $"主按钮: {(primaryPressed ? "按下" : "释放")}\n" +
                       $"副按钮: {(secondaryPressed ? "按下" : "释放")}\n" +
                       $"菜单: {(menuPressed ? "按下" : "释放")}";
        }
        
        // 检测按钮按下事件（边缘检测）
        if (triggerPressed && !lastTriggerState && CanProcessInput())
        {
            OnTriggerPressed(handName);
        }
        
        if (primaryPressed && !lastPrimaryState && CanProcessInput())
        {
            OnPrimaryButtonPressed(handName);
        }
        
        if (secondaryPressed && !lastSecondaryState && CanProcessInput())
        {
            OnSecondaryButtonPressed(handName);
        }
        
        if (menuPressed && !lastMenuState && CanProcessInput())
        {
            OnMenuButtonPressed(handName);
        }
        
        // 更新状态
        lastTriggerState = triggerPressed;
        lastPrimaryState = primaryPressed;
        lastSecondaryState = secondaryPressed;
        lastMenuState = menuPressed;
#endif
    }

    /// <summary>
    /// 检查是否可以处理输入
    /// </summary>
    private bool CanProcessInput()
    {
        if (Time.time - lastInputTime < inputCooldown)
        {
            return false;
        }
        
        lastInputTime = Time.time;
        return true;
    }

    /// <summary>
    /// 扳机键按下事件
    /// </summary>
    private void OnTriggerPressed(string inputSource)
    {
        UpdateFunctionInfo($"{inputSource}扳机键按下 - 执行第一部分功能");
        ExecuteFirstPartFunction();
    }

    /// <summary>
    /// 主按钮按下事件
    /// </summary>
    private void OnPrimaryButtonPressed(string inputSource)
    {
        UpdateFunctionInfo($"{inputSource}主按钮按下 - 执行第二部分功能");
        ExecuteSecondPartFunction();
    }

    /// <summary>
    /// 副按钮按下事件
    /// </summary>
    private void OnSecondaryButtonPressed(string inputSource)
    {
        UpdateFunctionInfo($"{inputSource}副按钮按下 - 重置位置");
        ExecuteResetFunction();
    }

    /// <summary>
    /// 菜单按钮按下事件
    /// </summary>
    private void OnMenuButtonPressed(string inputSource)
    {
        UpdateFunctionInfo($"{inputSource}菜单按钮按下 - 切换调试界面");
        ToggleDebugUI();
    }

    /// <summary>
    /// 执行第一部分功能
    /// </summary>
    private void ExecuteFirstPartFunction()
    {
        if (debugger == null)
        {
            UpdateFunctionInfo("错误: VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestCameraBasedPositioning();
        UpdateFunctionInfo("✓ 第一部分功能已执行");
    }

    /// <summary>
    /// 执行第二部分功能
    /// </summary>
    private void ExecuteSecondPartFunction()
    {
        if (debugger == null)
        {
            UpdateFunctionInfo("错误: VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.TestOrientation();
        UpdateFunctionInfo("✓ 第二部分功能已执行");
    }

    /// <summary>
    /// 执行重置功能
    /// </summary>
    private void ExecuteResetFunction()
    {
        if (debugger == null)
        {
            UpdateFunctionInfo("错误: VRAssemblyDebugger未找到！");
            return;
        }
        
        debugger.ResetPosition();
        UpdateFunctionInfo("✓ 重置功能已执行");
    }

    /// <summary>
    /// 创建调试UI
    /// </summary>
    private void CreateDebugUI()
    {
        // 创建Canvas
        GameObject canvasGO = new GameObject("Debug Canvas");
        debugCanvas = canvasGO.AddComponent<Canvas>();
        debugCanvas.renderMode = RenderMode.WorldSpace;
        debugCanvas.worldCamera = Camera.main;
        
        // 设置Canvas位置和大小
        var canvasTransform = debugCanvas.transform;
        canvasTransform.position = new Vector3(0, 2, 3);
        canvasTransform.localScale = Vector3.one * 0.01f;
        
        var canvasScaler = canvasGO.AddComponent<CanvasScaler>();
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        canvasScaler.referenceResolution = new Vector2(800, 600);
        
        canvasGO.AddComponent<GraphicRaycaster>();
        
        // 创建背景面板
        GameObject panelGO = new GameObject("Debug Panel");
        panelGO.transform.SetParent(canvasTransform);
        
        var panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.8f);
        
        var panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // 创建状态文本
        CreateDebugText("Status Text", panelGO.transform, new Vector2(0, 0.7f), ref statusText);
        
        // 创建输入文本
        CreateDebugText("Input Text", panelGO.transform, new Vector2(0, 0.3f), ref inputText);
        
        // 创建功能文本
        CreateDebugText("Function Text", panelGO.transform, new Vector2(0, -0.1f), ref functionText);
        
        UpdateFunctionInfo("PICO输入调试器已启动\n\n按键映射:\n扳机键 - 第一部分功能\n主按钮 - 第二部分功能\n副按钮 - 重置位置\n菜单键 - 切换界面");
    }

    /// <summary>
    /// 创建调试文本
    /// </summary>
    private void CreateDebugText(string name, Transform parent, Vector2 anchorPosition, ref Text textComponent)
    {
        GameObject textGO = new GameObject(name);
        textGO.transform.SetParent(parent);
        
        textComponent = textGO.AddComponent<Text>();
        textComponent.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        textComponent.fontSize = 24;
        textComponent.color = Color.white;
        textComponent.alignment = TextAnchor.MiddleCenter;
        
        var textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = new Vector2(0, anchorPosition.y);
        textRect.anchorMax = new Vector2(1, anchorPosition.y + 0.3f);
        textRect.offsetMin = new Vector2(20, 0);
        textRect.offsetMax = new Vector2(-20, 0);
    }

    /// <summary>
    /// 更新调试UI
    /// </summary>
    private void UpdateDebugUI()
    {
        if (statusText != null)
        {
            statusText.text = statusInfo;
        }
        
        if (inputText != null)
        {
            inputText.text = inputInfo;
        }
        
        if (functionText != null)
        {
            functionText.text = functionInfo;
        }
    }

    /// <summary>
    /// 更新状态信息
    /// </summary>
    private void UpdateStatusInfo(string info)
    {
        statusInfo = info;
    }

    /// <summary>
    /// 更新功能信息
    /// </summary>
    private void UpdateFunctionInfo(string info)
    {
        functionInfo = $"{System.DateTime.Now:HH:mm:ss} - {info}";
    }

    /// <summary>
    /// 切换调试UI显示
    /// </summary>
    private void ToggleDebugUI()
    {
        if (debugCanvas != null)
        {
            debugCanvas.gameObject.SetActive(!debugCanvas.gameObject.activeSelf);
            UpdateFunctionInfo($"调试界面: {(debugCanvas.gameObject.activeSelf ? "显示" : "隐藏")}");
        }
    }
}
