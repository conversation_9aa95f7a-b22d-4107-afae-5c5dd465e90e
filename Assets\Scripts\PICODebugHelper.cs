using UnityEngine;
using UnityEngine.UI;

#if UNITY_XR_INTERACTION_TOOLKIT
using UnityEngine.XR.Interaction.Toolkit;
using UnityEngine.InputSystem;
using UnityEngine.XR;
#endif

/// <summary>
/// PICO调试助手
/// 在设备屏幕上显示详细的调试信息
/// </summary>
public class PICODebugHelper : MonoBehaviour
{
    [Header("调试设置")]
    [SerializeField] private bool createDebugUI = true;
    [SerializeField] private bool enableContinuousLogging = true;
    [SerializeField] private float logInterval = 2f;

    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;

    // UI组件
    private Canvas debugCanvas;
    private Text debugText;
    private float lastLogTime = 0f;

    // 调试信息
    private string debugInfo = "";

    void Start()
    {
        // 强制启用详细日志
        Debug.unityLogger.logEnabled = true;

        Debug.Log("=== PICO调试助手启动 ===");

        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
        }

        // 立即设置初始调试信息
        debugInfo = "PICO调试助手已启动\n等待系统初始化...\n";

        if (createDebugUI)
        {
            CreateDebugUI();
        }

        StartCoroutine(InitializeWithDelay());
    }

    void Update()
    {
        if (enableContinuousLogging && Time.time - lastLogTime > logInterval)
        {
            LogSystemStatus();
            lastLogTime = Time.time;
        }

        UpdateDebugUI();
        
        // 键盘测试
        if (Input.GetKeyDown(KeyCode.T))
        {
            Debug.Log("[PICO调试] T键按下 - 测试装配区域定位");
            TestFunction1();
        }
        if (Input.GetKeyDown(KeyCode.P))
        {
            Debug.Log("[PICO调试] P键按下 - 测试装配面朝向");
            TestFunction2();
        }
    }

    /// <summary>
    /// 延迟初始化
    /// </summary>
    private System.Collections.IEnumerator InitializeWithDelay()
    {
        yield return new WaitForSeconds(1f);
        LogSystemStatus();
    }

    /// <summary>
    /// 记录系统状态
    /// </summary>
    private void LogSystemStatus()
    {
        string newDebugInfo = $"时间: {System.DateTime.Now:HH:mm:ss}\n";
        newDebugInfo += $"VRAssemblyDebugger: {(debugger != null ? "✅" : "❌")}\n";

#if UNITY_XR_INTERACTION_TOOLKIT
        newDebugInfo += "XR Interaction Toolkit: ✅\n";

        // 查找所有ActionBasedController
        var controllers = FindObjectsOfType<ActionBasedController>();
        newDebugInfo += $"ActionBasedController数量: {controllers.Length}\n";

        foreach (var controller in controllers)
        {
            newDebugInfo += $"控制器: {controller.name}\n";
            newDebugInfo += $"  Select Action: {(controller.selectAction.action != null ? "✅" : "❌")}\n";
            newDebugInfo += $"  Activate Action: {(controller.activateAction.action != null ? "✅" : "❌")}\n";

            if (controller.selectAction.action != null)
            {
                newDebugInfo += $"  Select 启用: {controller.selectAction.action.enabled}\n";
                newDebugInfo += $"  Select 值: {GetActionValue(controller.selectAction)}\n";
            }

            if (controller.activateAction.action != null)
            {
                newDebugInfo += $"  Activate 启用: {controller.activateAction.action.enabled}\n";
                newDebugInfo += $"  Activate 值: {GetActionValue(controller.activateAction)}\n";
            }
        }

        // 查找InputActionManager
        var inputManager = FindObjectOfType<InputActionManager>();
        if (inputManager != null)
        {
            newDebugInfo += $"InputActionManager: ✅\n";
            newDebugInfo += $"Action Assets数量: {inputManager.actionAssets.Count}\n";
        }
        else
        {
            newDebugInfo += "InputActionManager: ❌\n";
        }

        // 检查XR设备
        var xrDisplaySubsystems = new List<XRDisplaySubsystem>();
        SubsystemManager.GetInstances<XRDisplaySubsystem>(xrDisplaySubsystems);
        newDebugInfo += $"XR显示子系统: {xrDisplaySubsystems.Count}\n";

        var xrInputSubsystems = new List<XRInputSubsystem>();
        SubsystemManager.GetInstances<XRInputSubsystem>(xrInputSubsystems);
        newDebugInfo += $"XR输入子系统: {xrInputSubsystems.Count}\n";

#else
        newDebugInfo += "XR Interaction Toolkit: ❌\n";
#endif

        debugInfo = newDebugInfo;
        Debug.Log($"[PICO调试状态]\n{debugInfo}");
    }

#if UNITY_XR_INTERACTION_TOOLKIT
    /// <summary>
    /// 获取Action的值
    /// </summary>
    private float GetActionValue(InputActionProperty actionProperty)
    {
        if (actionProperty.action != null && actionProperty.action.enabled)
        {
            try
            {
                return actionProperty.action.ReadValue<float>();
            }
            catch
            {
                return 0f;
            }
        }
        return 0f;
    }
#endif

    /// <summary>
    /// 测试功能1
    /// </summary>
    public void TestFunction1()
    {
        Debug.Log("[PICO调试] 执行测试功能1 - 装配区域定位");
        if (debugger != null)
        {
            debugger.TestCameraBasedPositioning();
            debugInfo += "✅ 功能1已执行\n";
        }
        else
        {
            debugInfo += "❌ VRAssemblyDebugger未找到\n";
        }
    }

    /// <summary>
    /// 测试功能2
    /// </summary>
    public void TestFunction2()
    {
        Debug.Log("[PICO调试] 执行测试功能2 - 装配面朝向");
        if (debugger != null)
        {
            debugger.TestOrientation();
            debugInfo += "✅ 功能2已执行\n";
        }
        else
        {
            debugInfo += "❌ VRAssemblyDebugger未找到\n";
        }
    }

    /// <summary>
    /// 创建调试UI
    /// </summary>
    private void CreateDebugUI()
    {
        // 创建Canvas
        GameObject canvasGO = new GameObject("PICO Debug Canvas");
        debugCanvas = canvasGO.AddComponent<Canvas>();
        debugCanvas.renderMode = RenderMode.WorldSpace;
        
        // 设置Canvas位置
        var canvasTransform = debugCanvas.transform;
        canvasTransform.position = new Vector3(0, 1.5f, 2f);
        canvasTransform.localScale = Vector3.one * 0.005f;
        
        var canvasScaler = canvasGO.AddComponent<CanvasScaler>();
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        canvasScaler.referenceResolution = new Vector2(1000, 800);
        
        canvasGO.AddComponent<GraphicRaycaster>();
        
        // 创建背景面板
        GameObject panelGO = new GameObject("Debug Panel");
        panelGO.transform.SetParent(canvasTransform);
        
        var panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.9f);
        
        var panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // 创建调试文本
        GameObject textGO = new GameObject("Debug Text");
        textGO.transform.SetParent(panelGO.transform);
        
        debugText = textGO.AddComponent<Text>();
        debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        debugText.fontSize = 20;
        debugText.color = Color.white;
        debugText.alignment = TextAnchor.UpperLeft;
        
        var textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = new Vector2(20, 20);
        textRect.offsetMax = new Vector2(-20, -20);
        
        // 创建测试按钮
        CreateTestButton("测试功能1", new Vector2(0, -0.8f), TestFunction1);
        CreateTestButton("测试功能2", new Vector2(0.5f, -0.8f), TestFunction2);
    }

    /// <summary>
    /// 创建测试按钮
    /// </summary>
    private void CreateTestButton(string buttonText, Vector2 position, System.Action onClick)
    {
        GameObject buttonGO = new GameObject($"Button {buttonText}");
        buttonGO.transform.SetParent(debugCanvas.transform);
        
        var buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.6f, 1f, 0.8f);
        
        var button = buttonGO.AddComponent<Button>();
        button.onClick.AddListener(() => onClick?.Invoke());
        
        var buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(position.x, position.y);
        buttonRect.anchorMax = new Vector2(position.x + 0.4f, position.y + 0.15f);
        buttonRect.offsetMin = Vector2.zero;
        buttonRect.offsetMax = Vector2.zero;
        
        // 按钮文本
        GameObject textGO = new GameObject("Button Text");
        textGO.transform.SetParent(buttonGO.transform);
        
        var text = textGO.AddComponent<Text>();
        text.text = buttonText;
        text.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        text.fontSize = 16;
        text.color = Color.white;
        text.alignment = TextAnchor.MiddleCenter;
        
        var textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
    }

    /// <summary>
    /// 更新调试UI
    /// </summary>
    private void UpdateDebugUI()
    {
        if (debugText != null)
        {
            debugText.text = debugInfo;
        }
    }

    /// <summary>
    /// 强制记录状态
    /// </summary>
    [ContextMenu("强制记录状态")]
    public void ForceLogStatus()
    {
        LogSystemStatus();
    }
}
