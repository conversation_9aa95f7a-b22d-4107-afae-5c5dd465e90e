using UnityEngine;

/// <summary>
/// PICO VR输入设置助手
/// 帮助快速配置PICO手柄输入测试
/// </summary>
public class PICOVRInputSetup : MonoBehaviour
{
    [Header("自动设置")]
    [SerializeField] private bool autoSetupOnStart = true;
    [SerializeField] private bool showSetupLogs = true;

    [Header("组件引用")]
    [SerializeField] private VRAssemblyDebugger debugger;
    [SerializeField] private PICOVRInputAdapter inputAdapter;

    [Header("手柄输入映射")]
    [SerializeField] private bool enableTriggerPositioning = true;    // 扳机键 → 第一部分功能
    [SerializeField] private bool enablePrimaryOrientation = true;    // 主按钮 → 第二部分功能  
    [SerializeField] private bool enableSecondaryReset = true;        // 副按钮 → 重置位置
    [SerializeField] private bool enableMenuDebugToggle = true;       // 菜单键 → 切换调试

    void Start()
    {
        if (autoSetupOnStart)
        {
            SetupPICOVRInput();
        }
    }

    /// <summary>
    /// 设置PICO VR输入系统
    /// </summary>
    [ContextMenu("设置PICO VR输入")]
    public void SetupPICOVRInput()
    {
        if (showSetupLogs)
        {
            Debug.Log("=== 开始设置PICO VR输入系统 ===");
        }

        // 1. 查找或创建VRAssemblyDebugger
        SetupDebugger();

        // 2. 查找或创建PICOVRInputAdapter
        SetupInputAdapter();

        // 3. 配置输入映射
        ConfigureInputMapping();

        // 4. 验证设置
        ValidateSetup();

        if (showSetupLogs)
        {
            Debug.Log("=== PICO VR输入系统设置完成 ===");
            ShowUsageInstructions();
        }
    }

    /// <summary>
    /// 设置调试器
    /// </summary>
    private void SetupDebugger()
    {
        if (debugger == null)
        {
            debugger = FindObjectOfType<VRAssemblyDebugger>();
            if (debugger == null)
            {
                // 在场景中查找合适的GameObject来添加VRAssemblyDebugger
                GameObject debuggerGO = GameObject.Find("VRAssemblyDebugger");
                if (debuggerGO == null)
                {
                    debuggerGO = new GameObject("VRAssemblyDebugger");
                }
                debugger = debuggerGO.GetComponent<VRAssemblyDebugger>();
                if (debugger == null)
                {
                    debugger = debuggerGO.AddComponent<VRAssemblyDebugger>();
                }
                
                if (showSetupLogs)
                {
                    Debug.Log("✅ 已创建VRAssemblyDebugger组件");
                }
            }
            else
            {
                if (showSetupLogs)
                {
                    Debug.Log("✅ 找到现有的VRAssemblyDebugger组件");
                }
            }
        }
    }

    /// <summary>
    /// 设置输入适配器
    /// </summary>
    private void SetupInputAdapter()
    {
        if (inputAdapter == null)
        {
            inputAdapter = FindObjectOfType<PICOVRInputAdapter>();
            if (inputAdapter == null)
            {
                // 在场景中查找合适的GameObject来添加PICOVRInputAdapter
                GameObject adapterGO = GameObject.Find("PICO Input Adapter");
                if (adapterGO == null)
                {
                    adapterGO = new GameObject("PICO Input Adapter");
                }
                inputAdapter = adapterGO.GetComponent<PICOVRInputAdapter>();
                if (inputAdapter == null)
                {
                    inputAdapter = adapterGO.AddComponent<PICOVRInputAdapter>();
                }

                if (showSetupLogs)
                {
                    Debug.Log("✅ 已创建PICOVRInputAdapter组件");
                }
            }
            else
            {
                if (showSetupLogs)
                {
                    Debug.Log("✅ 找到现有的PICOVRInputAdapter组件");
                }
            }
        }
    }

    /// <summary>
    /// 配置输入映射
    /// </summary>
    private void ConfigureInputMapping()
    {
        if (inputAdapter != null && debugger != null)
        {
            // 使用反射设置PICOVRInputAdapter的debugger引用
            var debuggerField = inputAdapter.GetType().GetField("debugger", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (debuggerField != null)
            {
                debuggerField.SetValue(inputAdapter, debugger);
                if (showSetupLogs)
                {
                    Debug.Log("✅ 已配置PICOVRInputAdapter的debugger引用");
                }
            }

            // 配置功能映射
            var triggerField = inputAdapter.GetType().GetField("triggerTestsPositioning",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (triggerField != null)
            {
                triggerField.SetValue(inputAdapter, enableTriggerPositioning);
            }

            var primaryField = inputAdapter.GetType().GetField("primaryButtonTestsPreview",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (primaryField != null)
            {
                primaryField.SetValue(inputAdapter, enablePrimaryOrientation);
            }

            var secondaryField = inputAdapter.GetType().GetField("secondaryButtonTestsGuidance",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (secondaryField != null)
            {
                secondaryField.SetValue(inputAdapter, enableSecondaryReset);
            }

            var menuField = inputAdapter.GetType().GetField("menuButtonToggleDebug",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (menuField != null)
            {
                menuField.SetValue(inputAdapter, enableMenuDebugToggle);
            }

            if (showSetupLogs)
            {
                Debug.Log("✅ 已配置输入映射设置");
            }
        }
    }

    /// <summary>
    /// 验证设置
    /// </summary>
    private void ValidateSetup()
    {
        bool setupValid = true;

        if (debugger == null)
        {
            Debug.LogError("❌ VRAssemblyDebugger未设置");
            setupValid = false;
        }

        if (inputAdapter == null)
        {
            Debug.LogError("❌ PICOVRInputAdapter未设置");
            setupValid = false;
        }

        // 检查VRAssemblyPositioner
        var positioner = FindObjectOfType<VRAssemblyPositioner>();
        if (positioner == null)
        {
            Debug.LogWarning("⚠️ 未找到VRAssemblyPositioner，某些功能可能不可用");
        }

        // 检查XR Origin
        var xrOrigin = GameObject.Find("XR Origin (XR Rig)");
        if (xrOrigin == null)
        {
            Debug.LogWarning("⚠️ 未找到XR Origin，VR功能可能不可用");
        }

        // 检查控制器
        var leftController = GameObject.Find("LeftHand Controller");
        var rightController = GameObject.Find("RightHand Controller");
        if (leftController == null && rightController == null)
        {
            Debug.LogWarning("⚠️ 未找到VR控制器，将使用备用输入");
        }

        if (setupValid && showSetupLogs)
        {
            Debug.Log("✅ PICO VR输入系统验证通过");
        }
    }

    /// <summary>
    /// 显示使用说明
    /// </summary>
    private void ShowUsageInstructions()
    {
        string instructions = @"
🎮 PICO VR手柄操作说明：

右手控制器：
🔫 扳机键 (Trigger) → 第一部分功能：装配区域移动到摄像机固定位置
🅰️ 主按钮 (A/X) → 第二部分功能：装配面朝向摄像机
🅱️ 副按钮 (B/Y) → 重置装配区域位置
📋 菜单按钮 (Menu) → 切换调试界面

备用键盘输入（当VR不可用时）：
T键 → 第一部分功能
P键 → 第二部分功能
G键 → 重置位置
F1键 → 切换调试界面

💡 使用提示：
1. 确保PICO设备已连接并正确配置
2. 在Unity编辑器中可以使用键盘备用输入进行测试
3. 部署到PICO设备后使用手柄输入
4. 观察Console日志了解功能执行状态
";
        Debug.Log(instructions);
    }

    /// <summary>
    /// 测试输入系统
    /// </summary>
    [ContextMenu("测试输入系统")]
    public void TestInputSystem()
    {
        if (inputAdapter != null)
        {
            // 调用PICOVRInputAdapter的测试方法
            var testMethod = inputAdapter.GetType().GetMethod("TestAllVRFunctions");
            if (testMethod != null)
            {
                testMethod.Invoke(inputAdapter, null);
            }
        }
        else
        {
            Debug.LogError("PICOVRInputAdapter未设置，无法测试");
        }
    }

    /// <summary>
    /// 显示当前配置状态
    /// </summary>
    [ContextMenu("显示配置状态")]
    public void ShowConfigurationStatus()
    {
        Debug.Log("=== PICO VR输入配置状态 ===");
        Debug.Log($"VRAssemblyDebugger: {(debugger != null ? "✅ 已配置" : "❌ 未配置")}");
        Debug.Log($"PICOVRInputAdapter: {(inputAdapter != null ? "✅ 已配置" : "❌ 未配置")}");
        Debug.Log($"扳机键定位: {(enableTriggerPositioning ? "✅ 启用" : "❌ 禁用")}");
        Debug.Log($"主按钮朝向: {(enablePrimaryOrientation ? "✅ 启用" : "❌ 禁用")}");
        Debug.Log($"副按钮重置: {(enableSecondaryReset ? "✅ 启用" : "❌ 禁用")}");
        Debug.Log($"菜单键调试: {(enableMenuDebugToggle ? "✅ 启用" : "❌ 禁用")}");
        Debug.Log("========================");
    }
}
